# 操作记录目录说明

这个目录用于统一管理所有批量处理操作的记录，按照用户要求将每次操作的记录写到单独的文件夹中。

## 文件命名规则

### 结果文件
- **格式**: `batch_upload_results_{timestamp}.json`
- **示例**: `batch_upload_results_20250123_002030.json`
- **内容**: 详细的处理结果，包括每个演员的处理状态

### 统计文件
- **格式**: `batch_upload_stats_{timestamp}.txt`
- **示例**: `batch_upload_stats_20250123_002030.txt`
- **内容**: 简化的统计报告，便于快速查看

## 新增字段说明

### JSON结果文件新增字段
```json
{
  "total_profile_updates": 15,
  "total_gender_updates": 12,
  "results": [
    {
      "actor_id": 33,
      "actor_name": "王小亿",
      "profile_updated": true,
      "gender_updated": true,
      "profile_update_result": {...},
      "gender_update_result": {...}
    }
  ]
}
```

### 统计文件新增内容
```
更新profile总数: 15
更新性别总数: 12

详细结果:
- 演员: 王小亿 (ID: 33)
  状态: 成功
  创建cut: 0
  上传头像: 0
  更新profile: 是
  更新性别: 是
```

## 使用建议

1. **定期清理**: 建议定期清理旧的操作记录，保留最近的记录即可
2. **备份重要记录**: 对于重要的批量操作，建议备份操作记录
3. **错误排查**: 当操作失败时，可以查看详细的JSON文件进行错误排查
4. **进度跟踪**: 可以通过统计文件快速了解操作进度和结果

## 目录结构示例

```
operations_records/
├── README_操作记录说明.md
├── batch_upload_results_20250123_001500.json
├── batch_upload_stats_20250123_001500.txt
├── batch_upload_results_20250123_002030.json
├── batch_upload_stats_20250123_002030.txt
└── ...
```

## 注意事项

- 所有时间戳使用本地时间
- JSON文件使用UTF-8编码，确保中文正确显示
- 统计文件使用简体中文，便于阅读
- 文件大小会根据处理的演员数量而变化

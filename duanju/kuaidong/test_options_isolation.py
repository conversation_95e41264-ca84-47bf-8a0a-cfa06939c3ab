#!/usr/bin/env python3
"""
测试各个选项的隔离性
确保选项1-9都是互相隔离的，不会出现做A的同时顺带做了B的操作
"""

from batch_upload_processor import BatchUploadProcessor

def test_option_isolation():
    """测试各个选项的隔离性"""
    processor = BatchUploadProcessor()
    
    # 加载演员数据
    actors = processor.load_actors_data()
    if not actors:
        print("无法加载演员数据")
        return
    
    # 找一个测试演员
    test_actor = None
    for actor in actors:
        if actor.get('kuaidong_id') == "7387251624085438474":
            test_actor = actor
            break
    
    if not test_actor:
        print("未找到测试演员")
        return
    
    print(f"=== 测试演员：{test_actor.get('name')} ===")
    print(f"演员ID: {test_actor.get('id')}")
    print(f"kuaidong_id: {test_actor.get('kuaidong_id')}")
    print()
    
    # 测试各个选项的参数组合
    test_cases = [
        {
            "name": "选项1: 只处理头像",
            "params": {
                "process_all_videos": False,
                "process_head_images": True,
                "update_profile": False,
                "update_gender": False
            },
            "expected": {
                "should_process_videos": True,  # 选项1实际上是头像+第一个视频
                "should_process_head_images": True,
                "should_update_profile": False,
                "should_update_gender": False
            }
        },
        {
            "name": "选项2: 只处理视频",
            "params": {
                "process_all_videos": True,
                "process_head_images": False,
                "update_profile": False,
                "update_gender": False
            },
            "expected": {
                "should_process_videos": True,
                "should_process_head_images": False,
                "should_update_profile": False,
                "should_update_gender": False
            }
        },
        {
            "name": "选项7: 只更新profile",
            "params": {
                "process_all_videos": False,
                "process_head_images": False,
                "update_profile": True,
                "update_gender": False
            },
            "expected": {
                "should_process_videos": False,
                "should_process_head_images": False,
                "should_update_profile": True,
                "should_update_gender": False
            }
        },
        {
            "name": "选项8: 只更新性别",
            "params": {
                "process_all_videos": False,
                "process_head_images": False,
                "update_profile": False,
                "update_gender": True
            },
            "expected": {
                "should_process_videos": False,
                "should_process_head_images": False,
                "should_update_profile": False,
                "should_update_gender": True
            }
        },
        {
            "name": "选项9: 更新profile和性别",
            "params": {
                "process_all_videos": False,
                "process_head_images": False,
                "update_profile": True,
                "update_gender": True
            },
            "expected": {
                "should_process_videos": False,
                "should_process_head_images": False,
                "should_update_profile": True,
                "should_update_gender": True
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"=== {test_case['name']} ===")
        
        # 模拟处理逻辑判断
        params = test_case['params']
        expected = test_case['expected']
        
        # 判断是否会处理视频
        is_json_only_mode = params['update_profile'] or params['update_gender']
        will_process_videos = params['process_all_videos'] or (params['process_head_images'] and not is_json_only_mode)
        
        # 判断是否会处理头像
        will_process_head_images = params['process_head_images']
        
        # 判断是否会更新JSON数据
        will_update_profile = params['update_profile']
        will_update_gender = params['update_gender']
        
        print(f"参数: {params}")
        print(f"预期: {expected}")
        print(f"实际判断:")
        print(f"  - 会处理视频: {will_process_videos} (预期: {expected['should_process_videos']})")
        print(f"  - 会处理头像: {will_process_head_images} (预期: {expected['should_process_head_images']})")
        print(f"  - 会更新profile: {will_update_profile} (预期: {expected['should_update_profile']})")
        print(f"  - 会更新性别: {will_update_gender} (预期: {expected['should_update_gender']})")
        
        # 检查是否符合预期
        checks = [
            will_process_videos == expected['should_process_videos'],
            will_process_head_images == expected['should_process_head_images'],
            will_update_profile == expected['should_update_profile'],
            will_update_gender == expected['should_update_gender']
        ]
        
        if all(checks):
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        
        print()

if __name__ == "__main__":
    print("开始测试选项隔离性...")
    test_option_isolation()
    print("测试完成！")

#!/usr/bin/env python3
"""
增强版批量上传运行脚本
支持头像上传和演员信息更新
"""

import sys
import os
from batch_upload_processor import BatchUploadProcessor

def main():
    print("=== 增强版批量上传处理脚本 ===")
    print("功能：")
    print("1. 上传演员头像文件")
    print("2. 更新演员头像信息")
    print("3. 上传视频文件并创建cut")
    print("4. 更新演员profile信息（从JSON文件）")
    print("5. 更新演员性别信息（从JSON文件）")
    print("6. 统一管理操作记录")
    print()
    
    processor = BatchUploadProcessor()
    
    print("请选择处理模式：")
    print("1. 只处理头像（推荐用于首次头像上传）")
    print("2. 只处理视频（不处理头像）")
    print("3. 处理头像和第一个视频（默认模式）")
    print("4. 处理头像和所有视频（完整处理）")
    print("5. 只处理有视频的演员（头像+所有视频）")
    print("6. 自定义范围测试（前5个演员）")
    print("7. 只更新profile信息（从JSON文件）")
    print("8. 只更新性别信息（从JSON文件）")
    print("9. 更新profile和性别信息（从JSON文件）")
    
    try:
        choice = input("请输入选择 (1-9): ").strip()
        
        if choice == "1":
            print("开始处理所有演员的头像...")
            processor.process_all_actors(
                process_all_videos=False,
                process_head_images=True,
                only_with_videos=False
            )
            
        elif choice == "2":
            print("开始处理所有演员的视频（不处理头像）...")
            processor.process_all_actors(
                process_all_videos=True,
                process_head_images=False,
                only_with_videos=True
            )
            
        elif choice == "3":
            print("开始处理所有演员的头像和第一个视频...")
            processor.process_all_actors(
                process_all_videos=False,
                process_head_images=True,
                only_with_videos=False
            )
            
        elif choice == "4":
            print("开始完整处理所有演员（头像+所有视频）...")
            processor.process_all_actors(
                process_all_videos=True,
                process_head_images=True,
                only_with_videos=False
            )
            
        elif choice == "5":
            print("开始处理有视频的演员（头像+所有视频）...")
            processor.process_all_actors(
                process_all_videos=True,
                process_head_images=True,
                only_with_videos=True
            )
            
        elif choice == "6":
            print("开始测试处理前5个演员...")
            processor.process_all_actors(
                start_index=0,
                end_index=5,
                process_all_videos=True,
                process_head_images=True,
                only_with_videos=False
            )

        elif choice == "7":
            print("开始更新所有演员的profile信息...")
            processor.process_all_actors(
                process_all_videos=False,
                process_head_images=False,
                only_with_videos=False,
                update_profile=True,
                update_gender=False
            )

        elif choice == "8":
            print("开始更新所有演员的性别信息...")
            processor.process_all_actors(
                process_all_videos=False,
                process_head_images=False,
                only_with_videos=False,
                update_profile=False,
                update_gender=True
            )

        elif choice == "9":
            print("开始更新所有演员的profile和性别信息...")
            processor.process_all_actors(
                process_all_videos=False,
                process_head_images=False,
                only_with_videos=False,
                update_profile=True,
                update_gender=True
            )

        else:
            print("无效选择，使用默认模式（头像+第一个视频）...")
            processor.process_all_actors(
                process_all_videos=False,
                process_head_images=True,
                only_with_videos=False
            )
            
        print("\n处理完成！")
        print("结果已保存到 operations_records/ 目录")
        
    except KeyboardInterrupt:
        print("\n用户中断处理")
    except Exception as e:
        print(f"\n处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()

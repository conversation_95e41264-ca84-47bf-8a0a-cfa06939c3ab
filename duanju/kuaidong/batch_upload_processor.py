#!/usr/bin/env python3
"""
批量处理短剧演员视频上传脚本
功能：
1. 从视频文件中提取首帧作为封面
2. 上传封面图片到API
3. 上传视频文件到API
4. 创建短剧cut记录
5. 上传演员头像文件
6. 更新演员头像信息
"""

import json
import os
import glob
import subprocess
import requests
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_upload.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BatchUploadProcessor:
    def __init__(self):
        # API配置
        self.base_url = "https://data.wansu.tech/api"
        self.auth_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInRlbXAiOnRydWUsInNpZ25JblRpbWUiOjE3NTI5MTA1Njg2MTAsImlhdCI6MTc1MzE5NjU4OSwiZXhwIjoxNzUzMjgyOTg5LCJqdGkiOiIxNDE1NTQ5Ny1kYTEyLTRmNTEtOTQwMC03ZDZkYmQ5NjE0NGUifQ.a4TjbKvI_pwGbm3PaKaFQAmBgNeJo1SKj92RI23osxU"

        # 通用请求头
        self.headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'X-Authenticator': 'basic',
            'X-Timezone': '+08:00',
            'X-Hostname': 'data.wansu.tech',
            'X-Role': 'root',
            'X-With-ACL-Meta': 'true',
            'X-Locale': 'zh-CN',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        # 文件路径配置
        self.data_dir = "kuaidong_data"
        self.temp_dir = "temp"
        self.actors_file = "duanju_actors_with_kuaidong.json"

        # 操作记录目录
        self.operations_dir = "operations_records"

        # 创建必要目录
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.operations_dir, exist_ok=True)

        # 处理结果
        self.results = []

    def load_actors_data(self) -> List[Dict]:
        """加载演员数据"""
        try:
            with open(self.actors_file, 'r', encoding='utf-8') as f:
                actors = json.load(f)
            logger.info(f"成功加载 {len(actors)} 个演员数据")
            return actors
        except Exception as e:
            logger.error(f"加载演员数据失败: {e}")
            return []

    def extract_video_frame(self, video_path: str, output_path: str) -> bool:
        """从视频中提取首帧作为封面"""
        try:
            cmd = [
                'ffmpeg', '-i', video_path,
                '-vframes', '1',
                '-f', 'image2',
                '-y',  # 覆盖输出文件
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"成功提取视频首帧: {video_path} -> {output_path}")
                return True
            else:
                logger.error(f"提取视频首帧失败: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"提取视频首帧异常: {e}")
            return False

    def upload_file(self, file_path: str, attachment_field: str) -> Optional[Dict]:
        """上传文件到API"""
        try:
            url = f"{self.base_url}/attachments:create?attachmentField={attachment_field}"

            filename = os.path.basename(file_path)

            # 根据文件类型设置MIME类型
            if file_path.endswith('.png'):
                mime_type = 'image/png'
            elif file_path.endswith('.jpg') or file_path.endswith('.jpeg'):
                mime_type = 'image/jpeg'
            elif file_path.endswith('.mp4'):
                mime_type = 'video/mp4'
            else:
                mime_type = 'application/octet-stream'

            # 创建不包含Content-Type的headers副本（让requests自动处理multipart/form-data）
            upload_headers = self.headers.copy()
            # 确保没有Content-Type设置，让requests自动处理
            upload_headers.pop('Content-Type', None)

            logger.info(f"开始上传文件: {filename} 到 {attachment_field}")

            with open(file_path, 'rb') as f:
                files = {
                    'file': (filename, f, mime_type)
                }

                response = requests.post(url, headers=upload_headers, files=files, timeout=300)

            logger.info(f"上传响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                logger.info(f"文件上传成功: {filename}")
                logger.debug(f"上传响应内容: {result}")

                # 检查响应是否包含正确的文件信息
                # API返回的数据结构是 {'data': {...}}
                if 'data' in result and 'url' in result['data']:
                    file_url = result['data']['url']
                    if file_url and not file_url.endswith('unknown-200-200.png'):
                        logger.info(f"文件URL: {file_url}")
                        return result['data']  # 返回data部分，保持与原有逻辑一致
                    else:
                        logger.error(f"上传响应异常，返回了占位符URL: {file_url}")
                        return None
                else:
                    logger.error(f"上传响应格式异常: {result}")
                    return None
            else:
                logger.error(f"文件上传失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"文件上传异常: {e}")
            return None

    def create_cut(self, actor_id: str, cover_data: Dict, video_data: Dict, 
                   title: str = "Cut 1", desc: str = "描述", 
                   play_count: int = 10, duration: int = 20) -> Optional[Dict]:
        """创建短剧cut"""
        try:
            url = f"{self.base_url}/duanju_actors/{actor_id}/cuts:create"
            
            data = {
                "desc": desc,
                "title": title,
                "video": video_data,
                "play_count": play_count,
                "duration": duration,
                "cover": cover_data
            }
            
            headers = self.headers.copy()
            headers['Content-Type'] = 'application/json'
            
            response = requests.post(url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"成功创建cut: actor_id={actor_id}")
                return result
            else:
                logger.error(f"创建cut失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"创建cut异常: {e}")
            return None

    def find_video_files(self, kuaidong_id: str) -> List[str]:
        """查找指定kuaidong_id对应的视频文件"""
        video_dir = os.path.join(self.data_dir, kuaidong_id, "videos")
        if not os.path.exists(video_dir):
            return []

        video_files = glob.glob(os.path.join(video_dir, "*.mp4"))
        return video_files

    def find_head_image_files(self, kuaidong_id: str) -> List[str]:
        """查找指定kuaidong_id对应的头像文件"""
        images_dir = os.path.join(self.data_dir, kuaidong_id, "images")
        if not os.path.exists(images_dir):
            return []

        # 查找头像文件（通常以_head_开头）
        head_files = glob.glob(os.path.join(images_dir, "*_head_*.png"))
        head_files.extend(glob.glob(os.path.join(images_dir, "*_head_*.jpg")))
        head_files.extend(glob.glob(os.path.join(images_dir, "*_head_*.jpeg")))
        return head_files

    def find_json_save_file(self, kuaidong_id: str) -> Optional[str]:
        """查找指定kuaidong_id对应的.saved.json文件"""
        json_dir = os.path.join(self.data_dir, kuaidong_id, "json")
        if not os.path.exists(json_dir):
            return None

        # 查找.saved.json文件
        json_files = glob.glob(os.path.join(json_dir, "*.saved.json"))
        if json_files:
            return json_files[0]  # 返回第一个找到的文件
        return None

    def load_actor_json_data(self, kuaidong_id: str) -> Optional[Dict]:
        """加载演员的JSON数据"""
        json_file = self.find_json_save_file(kuaidong_id)
        if not json_file:
            logger.warning(f"未找到演员 {kuaidong_id} 的JSON文件")
            return None

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载演员 {kuaidong_id} 的JSON数据")
            return data
        except Exception as e:
            logger.error(f"加载演员 {kuaidong_id} 的JSON数据失败: {e}")
            return None

    def extract_profile_from_json(self, json_data: Dict) -> Optional[str]:
        """从JSON数据中提取profile（content_markdown）"""
        try:
            content_markdown = json_data.get('content_markdown')
            if content_markdown and content_markdown.strip():
                logger.info("成功提取profile数据")
                return content_markdown.strip()
            else:
                logger.warning("JSON数据中没有找到有效的content_markdown")
                return None
        except Exception as e:
            logger.error(f"提取profile数据失败: {e}")
            return None

    def extract_gender_from_json(self, json_data: Dict) -> Optional[str]:
        """从JSON数据中提取性别信息"""
        try:
            infobox_parsed = json_data.get('infobox_parsed', {})
            gender = infobox_parsed.get('性别')
            if gender and gender.strip():
                # 标准化性别值
                gender_normalized = gender.strip().lower()
                if gender_normalized in ['男', 'male', '男性']:
                    return 'male'
                elif gender_normalized in ['女', 'female', '女性']:
                    return 'female'
                else:
                    logger.warning(f"未识别的性别值: {gender}")
                    return gender.strip()  # 返回原始值
            else:
                logger.warning("JSON数据中没有找到性别信息")
                return None
        except Exception as e:
            logger.error(f"提取性别信息失败: {e}")
            return None

    def upload_head_image(self, file_path: str) -> Optional[Dict]:
        """上传头像文件到API"""
        try:
            url = f"{self.base_url}/attachments:create?attachmentField=duanju_actors.head_images"

            filename = os.path.basename(file_path)

            # 根据文件类型设置MIME类型
            if file_path.endswith('.png'):
                mime_type = 'image/png'
            elif file_path.endswith('.jpg') or file_path.endswith('.jpeg'):
                mime_type = 'image/jpeg'
            else:
                mime_type = 'image/png'  # 默认为PNG

            # 创建不包含Content-Type的headers副本（让requests自动处理multipart/form-data）
            upload_headers = self.headers.copy()
            # 确保没有Content-Type设置，让requests自动处理
            upload_headers.pop('Content-Type', None)

            logger.info(f"开始上传头像: {filename}")

            with open(file_path, 'rb') as f:
                files = {
                    'file': (filename, f, mime_type)
                }

                response = requests.post(url, headers=upload_headers, files=files, timeout=300)

            logger.info(f"头像上传响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                logger.info(f"头像上传成功: {filename}")
                logger.debug(f"头像上传响应内容: {result}")

                # 检查响应是否包含正确的文件信息
                # API返回的数据结构是 {'data': {...}}
                if 'data' in result and 'url' in result['data']:
                    file_url = result['data']['url']
                    if file_url and not file_url.endswith('unknown-200-200.png'):
                        logger.info(f"头像URL: {file_url}")
                        return result['data']  # 返回data部分，保持与原有逻辑一致
                    else:
                        logger.error(f"头像上传响应异常，返回了占位符URL: {file_url}")
                        return None
                else:
                    logger.error(f"头像上传响应格式异常: {result}")
                    return None
            else:
                logger.error(f"头像上传失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"头像上传异常: {e}")
            return None

    def update_actor_head_images(self, actor_id: str, head_images_data: List[Dict]) -> Optional[Dict]:
        """更新演员的头像信息"""
        try:
            url = f"{self.base_url}/duanju_actors:update?filterByTk={actor_id}"

            # 构建更新数据，只更新head_images字段
            data = {
                "head_images": head_images_data
            }

            headers = self.headers.copy()
            headers['Content-Type'] = 'application/json'

            response = requests.patch(url, headers=headers, json=data, timeout=60)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"演员头像信息更新成功: actor_id={actor_id}")
                return result
            else:
                logger.error(f"演员头像信息更新失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"演员头像信息更新异常: {e}")
            return None

    def update_actor_profile(self, actor_id: str, profile: str) -> Optional[Dict]:
        """更新演员的profile信息"""
        try:
            url = f"{self.base_url}/duanju_actors:update?filterByTk={actor_id}"

            # 构建更新数据，只更新profile字段
            data = {
                "profile": profile
            }

            headers = self.headers.copy()
            headers['Content-Type'] = 'application/json'

            response = requests.patch(url, headers=headers, json=data, timeout=60)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"演员profile信息更新成功: actor_id={actor_id}")
                return result
            else:
                logger.error(f"演员profile信息更新失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"演员profile信息更新异常: {e}")
            return None

    def update_actor_gender(self, actor_id: str, gender: str) -> Optional[Dict]:
        """更新演员的性别信息"""
        try:
            url = f"{self.base_url}/duanju_actors:update?filterByTk={actor_id}"

            # 构建更新数据，只更新gender字段
            data = {
                "gender": gender
            }

            headers = self.headers.copy()
            headers['Content-Type'] = 'application/json'

            response = requests.patch(url, headers=headers, json=data, timeout=60)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"演员性别信息更新成功: actor_id={actor_id}, gender={gender}")
                return result
            else:
                logger.error(f"演员性别信息更新失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"演员性别信息更新异常: {e}")
            return None

    def process_actor_head_images(self, kuaidong_id: str, actor_id: str, actor_name: str) -> Dict:
        """处理演员的所有头像文件"""
        result = {
            'uploaded_images': [],
            'update_result': None,
            'error': None
        }

        try:
            # 查找头像文件
            head_image_files = self.find_head_image_files(kuaidong_id)
            if not head_image_files:
                result['error'] = f"未找到头像文件: {kuaidong_id}"
                logger.info(result['error'])
                return result

            logger.info(f"找到 {len(head_image_files)} 个头像文件")

            # 上传所有头像文件
            uploaded_images_data = []
            for head_image_file in head_image_files:
                upload_result = self.upload_head_image(head_image_file)
                if upload_result:
                    uploaded_images_data.append(upload_result)
                    result['uploaded_images'].append({
                        'file_path': head_image_file,
                        'upload_result': upload_result
                    })
                    logger.info(f"头像上传成功: {os.path.basename(head_image_file)}")
                else:
                    logger.error(f"头像上传失败: {head_image_file}")

            # 如果有成功上传的头像，更新演员信息
            if uploaded_images_data:
                update_result = self.update_actor_head_images(actor_id, uploaded_images_data)
                result['update_result'] = update_result
                if update_result:
                    logger.info(f"演员 {actor_name} 头像信息更新成功")
                else:
                    result['error'] = "头像信息更新失败"
            else:
                result['error'] = "没有成功上传的头像"

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"处理演员头像时发生异常: {e}")

        return result

    def process_actor_json_data(self, kuaidong_id: str, actor_id: str, actor_name: str,
                               update_profile: bool = False, update_gender: bool = False) -> Dict:
        """处理演员的JSON数据，更新profile和性别信息"""
        result = {
            'profile_updated': False,
            'gender_updated': False,
            'profile_update_result': None,
            'gender_update_result': None,
            'error': None
        }

        try:
            # 加载JSON数据
            json_data = self.load_actor_json_data(kuaidong_id)
            if not json_data:
                result['error'] = f"无法加载演员 {kuaidong_id} 的JSON数据"
                return result

            # 更新profile
            if update_profile:
                profile = self.extract_profile_from_json(json_data)
                if profile:
                    profile_result = self.update_actor_profile(actor_id, profile)
                    if profile_result:
                        result['profile_updated'] = True
                        result['profile_update_result'] = profile_result
                        logger.info(f"演员 {actor_name} profile更新成功")
                    else:
                        result['error'] = "profile更新失败"
                else:
                    result['error'] = "未找到有效的profile数据"

            # 更新性别
            if update_gender:
                gender = self.extract_gender_from_json(json_data)
                if gender:
                    gender_result = self.update_actor_gender(actor_id, gender)
                    if gender_result:
                        result['gender_updated'] = True
                        result['gender_update_result'] = gender_result
                        logger.info(f"演员 {actor_name} 性别更新成功: {gender}")
                    else:
                        if not result['error']:
                            result['error'] = "性别更新失败"
                else:
                    if not result['error']:
                        result['error'] = "未找到有效的性别数据"

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"处理演员JSON数据时发生异常: {e}")

        return result

    def process_actor(self, actor: Dict, process_all_videos: bool = False,
                     process_head_images: bool = True, update_profile: bool = False,
                     update_gender: bool = False) -> Dict:
        """处理单个演员"""
        kuaidong_id = actor.get('kuaidong_id')
        actor_id = actor.get('id')
        actor_name = actor.get('name', 'Unknown')

        result = {
            'actor_id': actor_id,
            'kuaidong_id': kuaidong_id,
            'actor_name': actor_name,
            'success': False,
            'error_message': None,
            'cuts_created': [],
            'head_images_uploaded': [],
            'head_images_update_result': None,
            'profile_updated': False,
            'gender_updated': False,
            'profile_update_result': None,
            'gender_update_result': None,
            'processed_at': datetime.now().isoformat()
        }

        try:
            logger.info(f"开始处理演员: {actor_name} (ID: {actor_id}, kuaidong_id: {kuaidong_id})")

            # 检查必要参数
            if not kuaidong_id or not actor_id:
                result['error_message'] = f"缺少必要参数: kuaidong_id={kuaidong_id}, actor_id={actor_id}"
                logger.warning(result['error_message'])
                return result

            # 处理头像文件
            if process_head_images:
                head_image_result = self.process_actor_head_images(kuaidong_id, actor_id, actor_name)
                result['head_images_uploaded'] = head_image_result['uploaded_images']
                result['head_images_update_result'] = head_image_result['update_result']
                if head_image_result['error']:
                    logger.warning(f"头像处理有问题: {head_image_result['error']}")

            # 处理JSON数据（profile和性别）
            if update_profile or update_gender:
                json_result = self.process_actor_json_data(kuaidong_id, actor_id, actor_name,
                                                         update_profile, update_gender)
                result['profile_updated'] = json_result['profile_updated']
                result['gender_updated'] = json_result['gender_updated']
                result['profile_update_result'] = json_result['profile_update_result']
                result['gender_update_result'] = json_result['gender_update_result']
                if json_result['error']:
                    logger.warning(f"JSON数据处理有问题: {json_result['error']}")

            # 判断是否需要处理视频
            # 只有在以下情况才处理视频：
            # 1. 明确要求处理所有视频 (process_all_videos=True)
            # 2. 传统的头像+视频模式：process_head_images=True 且不是纯JSON更新模式
            is_json_only_mode = update_profile or update_gender
            need_process_videos = process_all_videos or (process_head_images and not is_json_only_mode)

            if need_process_videos:
                # 查找视频文件
                video_files = self.find_video_files(kuaidong_id)
                if not video_files:
                    result['error_message'] = f"未找到视频文件: {kuaidong_id}"
                    logger.warning(result['error_message'])
                    # 如果头像处理成功或JSON数据处理成功，仍然算作部分成功
                    if result['head_images_uploaded'] or result['profile_updated'] or result['gender_updated']:
                        result['success'] = True
                    return result

                logger.info(f"找到 {len(video_files)} 个视频文件")

                # 决定处理哪些视频文件
                videos_to_process = video_files if process_all_videos else [video_files[0]]

                for i, video_file in enumerate(videos_to_process):
                    cut_result = self.process_single_video(
                        actor_id, actor_name, kuaidong_id, video_file, i + 1
                    )
                    if cut_result['success']:
                        result['cuts_created'].append(cut_result)
                    else:
                        # 如果某个视频处理失败，记录错误但继续处理其他视频
                        logger.error(f"视频 {video_file} 处理失败: {cut_result['error_message']}")

            if (result['cuts_created'] or result['head_images_uploaded'] or
                result['profile_updated'] or result['gender_updated']):
                result['success'] = True
                success_parts = []
                if result['cuts_created']:
                    success_parts.append(f"创建 {len(result['cuts_created'])} 个cut")
                if result['head_images_uploaded']:
                    success_parts.append(f"上传 {len(result['head_images_uploaded'])} 个头像")
                if result['profile_updated']:
                    success_parts.append("更新profile")
                if result['gender_updated']:
                    success_parts.append("更新性别")
                logger.info(f"演员 {actor_name} 处理完成：{', '.join(success_parts)}")
            else:
                result['error_message'] = "所有处理都失败了"

        except Exception as e:
            result['error_message'] = str(e)
            logger.error(f"处理演员 {actor_name} 时发生异常: {e}")

        return result

    def process_single_video(self, actor_id: str, actor_name: str, kuaidong_id: str,
                           video_file: str, cut_number: int) -> Dict:
        """处理单个视频文件"""
        cut_result = {
            'video_file': video_file,
            'cut_number': cut_number,
            'success': False,
            'error_message': None,
            'cover_upload_result': None,
            'video_upload_result': None,
            'cut_create_result': None
        }

        try:
            logger.info(f"处理视频文件 {cut_number}: {video_file}")

            # 提取视频首帧
            cover_filename = f"{kuaidong_id}_cover_{cut_number}.png"
            cover_path = os.path.join(self.temp_dir, cover_filename)

            if not self.extract_video_frame(video_file, cover_path):
                cut_result['error_message'] = "提取视频首帧失败"
                return cut_result

            # 上传封面
            cover_result = self.upload_file(cover_path, "duanju_actor_cuts.cover")
            if not cover_result:
                cut_result['error_message'] = "上传封面失败"
                return cut_result
            cut_result['cover_upload_result'] = cover_result

            # 上传视频
            video_result = self.upload_file(video_file, "duanju_actor_cuts.video")
            if not video_result:
                cut_result['error_message'] = "上传视频失败"
                return cut_result
            cut_result['video_upload_result'] = video_result

            # 创建cut
            create_result = self.create_cut(
                actor_id=actor_id,
                cover_data=cover_result,
                video_data=video_result,
                title=f"{actor_name} - Cut {cut_number}"
            )
            if not create_result:
                cut_result['error_message'] = "创建cut失败"
                return cut_result
            cut_result['cut_create_result'] = create_result

            cut_result['success'] = True
            logger.info(f"视频 {cut_number} 处理完成")

            # 清理临时文件
            if os.path.exists(cover_path):
                os.remove(cover_path)

        except Exception as e:
            cut_result['error_message'] = str(e)
            logger.error(f"处理视频文件 {video_file} 时发生异常: {e}")

        return cut_result

    def process_all_actors(self, start_index: int = 0, end_index: Optional[int] = None,
                          process_all_videos: bool = False, process_head_images: bool = True,
                          only_with_videos: bool = False, update_profile: bool = False,
                          update_gender: bool = False):
        """批量处理所有演员"""
        actors = self.load_actors_data()
        if not actors:
            logger.error("没有加载到演员数据")
            return

        if end_index is None:
            end_index = len(actors)

        actors_to_process = actors[start_index:end_index]

        # 如果只处理有视频的演员，先过滤
        if only_with_videos:
            actors_with_videos = []
            for actor in actors_to_process:
                kuaidong_id = actor.get('kuaidong_id')
                if kuaidong_id and self.find_video_files(kuaidong_id):
                    actors_with_videos.append(actor)
            actors_to_process = actors_with_videos
            logger.info(f"过滤后有 {len(actors_to_process)} 个演员有视频文件")
        else:
            # 过滤掉没有kuaidong_id的演员
            actors_with_kuaidong = []
            for actor in actors_to_process:
                kuaidong_id = actor.get('kuaidong_id')
                if kuaidong_id:
                    actors_with_kuaidong.append(actor)
            actors_to_process = actors_with_kuaidong
            logger.info(f"过滤后有 {len(actors_to_process)} 个演员有kuaidong_id")

        total_count = len(actors_to_process)

        if total_count == 0:
            logger.warning("没有找到需要处理的演员")
            return

        logger.info(f"开始批量处理，共 {total_count} 个演员")
        if process_all_videos:
            logger.info("将处理每个演员的所有视频文件")
        if process_head_images:
            logger.info("将处理每个演员的头像文件")
        if update_profile:
            logger.info("将更新每个演员的profile信息")
        if update_gender:
            logger.info("将更新每个演员的性别信息")

        for i, actor in enumerate(actors_to_process):
            logger.info(f"进度: {i+1}/{total_count}")
            result = self.process_actor(
                actor,
                process_all_videos=process_all_videos,
                process_head_images=process_head_images,
                update_profile=update_profile,
                update_gender=update_gender
            )
            self.results.append(result)

            # 每处理一个演员后稍作延迟，避免API限流
            time.sleep(1)

        # 保存结果
        self.save_results()

    def save_results(self):
        """保存处理结果到操作记录目录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(self.operations_dir, f"batch_upload_results_{timestamp}.json")

        # 统计信息
        total_cuts = sum(len(r.get('cuts_created', [])) for r in self.results)
        total_head_images = sum(len(r.get('head_images_uploaded', [])) for r in self.results)
        total_profile_updates = sum(1 for r in self.results if r.get('profile_updated', False))
        total_gender_updates = sum(1 for r in self.results if r.get('gender_updated', False))

        summary = {
            'total_processed': len(self.results),
            'successful': len([r for r in self.results if r['success']]),
            'failed': len([r for r in self.results if not r['success']]),
            'total_cuts_created': total_cuts,
            'total_head_images_uploaded': total_head_images,
            'total_profile_updates': total_profile_updates,
            'total_gender_updates': total_gender_updates,
            'processed_at': datetime.now().isoformat(),
            'results': self.results
        }

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logger.info(f"处理结果已保存到: {results_file}")
        logger.info(f"处理统计: 总计 {summary['total_processed']}, 成功 {summary['successful']}, 失败 {summary['failed']}")
        logger.info(f"创建cut: {total_cuts}, 上传头像: {total_head_images}, 更新profile: {total_profile_updates}, 更新性别: {total_gender_updates}")

        # 同时保存一份简化的统计报告
        stats_file = os.path.join(self.operations_dir, f"batch_upload_stats_{timestamp}.txt")
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write(f"批量上传处理统计报告\n")
            f.write(f"处理时间: {summary['processed_at']}\n")
            f.write(f"总处理演员数: {summary['total_processed']}\n")
            f.write(f"成功处理: {summary['successful']}\n")
            f.write(f"失败处理: {summary['failed']}\n")
            f.write(f"创建cut总数: {total_cuts}\n")
            f.write(f"上传头像总数: {total_head_images}\n")
            f.write(f"更新profile总数: {total_profile_updates}\n")
            f.write(f"更新性别总数: {total_gender_updates}\n\n")

            f.write("详细结果:\n")
            for result in self.results:
                f.write(f"- 演员: {result['actor_name']} (ID: {result['actor_id']})\n")
                f.write(f"  状态: {'成功' if result['success'] else '失败'}\n")
                if result.get('error_message'):
                    f.write(f"  错误: {result['error_message']}\n")
                f.write(f"  创建cut: {len(result.get('cuts_created', []))}\n")
                f.write(f"  上传头像: {len(result.get('head_images_uploaded', []))}\n")
                f.write(f"  更新profile: {'是' if result.get('profile_updated', False) else '否'}\n")
                f.write(f"  更新性别: {'是' if result.get('gender_updated', False) else '否'}\n")
                f.write("\n")

        logger.info(f"统计报告已保存到: {stats_file}")

if __name__ == "__main__":
    processor = BatchUploadProcessor()

    # 使用示例：

    # 1. 只处理头像，不处理视频（推荐用于首次头像上传）
    # processor.process_all_actors(
    #     start_index=0,
    #     end_index=5,  # 测试前5个演员
    #     process_all_videos=False,
    #     process_head_images=True,
    #     only_with_videos=False
    # )

    # 2. 处理所有视频和头像（完整处理）
    # processor.process_all_actors(
    #     process_all_videos=True,
    #     process_head_images=True,
    #     only_with_videos=False
    # )

    # 3. 只处理有视频的演员，包含头像和所有视频
    # processor.process_all_actors(
    #     process_all_videos=True,
    #     process_head_images=True,
    #     only_with_videos=True
    # )

    # 默认：处理所有演员的头像和第一个视频
    processor.process_all_actors(
        process_all_videos=False,
        process_head_images=True,
        only_with_videos=False
    )
